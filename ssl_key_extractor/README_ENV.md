# SSL Key Log 环境变量方法

## 🌟 简介

使用 `SSLKEYLOGFILE` 环境变量是获取 SSL 密钥的**最简单、最可靠**的方法。相比复杂的代码注入，环境变量方法具有以下优势：

- ✅ **官方支持** - OpenSSL 原生支持
- ✅ **无需特殊权限** - 不需要 root 权限
- ✅ **配置简单** - 只需设置环境变量
- ✅ **格式标准** - Wireshark 直接支持
- ✅ **更安全** - 不需要代码注入

## 🚀 快速开始

### 1. 基本使用

```bash
# 设置环境变量
export SSLKEYLOGFILE="/tmp/ssl_keys.log"

# 执行 HTTPS 请求
curl -k https://httpbin.org/get

# 查看提取的密钥
cat /tmp/ssl_keys.log
```

### 2. 使用配置脚本

```bash
# 给脚本执行权限
chmod +x setup_env_keylog.sh

# 启用用户级配置
./setup_env_keylog.sh enable

# 启用全局配置（需要 root 权限）
sudo ./setup_env_keylog.sh enable --global

# 为特定服务启用（如 nginx）
sudo ./setup_env_keylog.sh enable --service nginx

# 查看状态
./setup_env_keylog.sh status

# 测试功能
./setup_env_keylog.sh test

# 禁用配置
./setup_env_keylog.sh disable
```

## 📋 配置方法

### 方法1：临时设置（当前会话）

```bash
export SSLKEYLOGFILE="/tmp/ssl_keys.log"
```

### 方法2：用户级永久设置

```bash
# 添加到 ~/.bashrc 或 ~/.zshrc
echo 'export SSLKEYLOGFILE="/tmp/ssl_keys.log"' >> ~/.bashrc
source ~/.bashrc
```

### 方法3：全局设置（所有用户）

```bash
# 创建系统级环境变量文件
sudo tee /etc/profile.d/ssl-keylog.sh << EOF
export SSLKEYLOGFILE="/tmp/ssl_keys.log"
EOF
```

### 方法4：服务级设置（systemd 服务）

```bash
# 为 nginx 服务设置
sudo mkdir -p /etc/systemd/system/nginx.service.d
sudo tee /etc/systemd/system/nginx.service.d/ssl-keylog.conf << EOF
[Service]
Environment="SSLKEYLOGFILE=/var/log/nginx/ssl_keys.log"
EOF

sudo systemctl daemon-reload
sudo systemctl restart nginx
```

## 🧪 测试验证

### 测试 curl

```bash
export SSLKEYLOGFILE="/tmp/test_curl.log"
curl -k https://httpbin.org/get
cat /tmp/test_curl.log
```

### 测试 wget

```bash
export SSLKEYLOGFILE="/tmp/test_wget.log"
wget --no-check-certificate -O /dev/null https://www.google.com
cat /tmp/test_wget.log
```

### 测试 Python requests

```bash
export SSLKEYLOGFILE="/tmp/test_python.log"
python3 -c "
import ssl
import urllib.request
urllib.request.urlopen('https://httpbin.org/get', context=ssl._create_unverified_context())
"
cat /tmp/test_python.log
```

## 📊 支持的程序

### ✅ 完全支持
- **curl** - 完美支持
- **wget** - 完美支持  
- **Python requests/urllib** - 完美支持
- **Node.js https** - 完美支持
- **Go net/http** - 完美支持
- **Java HttpsURLConnection** - 部分支持

### ⚠️ 部分支持
- **nginx** - 需要特殊配置
- **Apache** - 需要特殊配置
- **Chrome/Firefox** - 支持但需要启动参数

### ❌ 不支持
- 使用非 OpenSSL 库的程序
- 静态链接 SSL 库的程序
- 某些嵌入式程序

## 🔧 高级配置

### 自动轮转日志

```bash
# 创建带时间戳的日志文件
export SSLKEYLOGFILE="/var/log/ssl_keys_$(date +%Y%m%d_%H%M%S).log"
```

### 多进程安全

```bash
# 使用进程 ID 区分不同进程
export SSLKEYLOGFILE="/tmp/ssl_keys_$$.log"
```

### 条件启用

```bash
# 只在调试模式下启用
if [[ "$DEBUG" == "1" ]]; then
    export SSLKEYLOGFILE="/tmp/debug_ssl_keys.log"
fi
```

## 🔍 故障排除

### 问题1：没有生成密钥文件

**可能原因：**
- 程序不支持 SSLKEYLOGFILE
- OpenSSL 版本过旧
- 使用了其他 SSL 库

**解决方法：**
```bash
# 检查 OpenSSL 版本
openssl version

# 检查程序是否链接 OpenSSL
ldd $(which curl) | grep ssl

# 使用 strace 跟踪文件操作
strace -e trace=openat curl -k https://httpbin.org/get 2>&1 | grep ssl_keys
```

### 问题2：权限错误

**解决方法：**
```bash
# 确保目录存在且有写权限
mkdir -p $(dirname "$SSLKEYLOGFILE")
chmod 755 $(dirname "$SSLKEYLOGFILE")
touch "$SSLKEYLOGFILE"
chmod 600 "$SSLKEYLOGFILE"
```

### 问题3：服务不生效

**解决方法：**
```bash
# 检查服务环境变量
sudo systemctl show nginx --property=Environment

# 重新加载配置
sudo systemctl daemon-reload
sudo systemctl restart nginx
```

## 📈 与 Wireshark 集成

### 1. 配置 Wireshark

1. 打开 Wireshark
2. 进入 `Edit` → `Preferences`
3. 展开 `Protocols` → `TLS`
4. 在 `(Pre)-Master-Secret log filename` 中输入密钥文件路径
5. 点击 `OK`

### 2. 实时分析

```bash
# 启动抓包
sudo tcpdump -i any -w /tmp/https_traffic.pcap port 443 &

# 设置密钥记录
export SSLKEYLOGFILE="/tmp/ssl_keys.log"

# 执行 HTTPS 请求
curl -k https://httpbin.org/get

# 停止抓包
sudo pkill tcpdump

# 在 Wireshark 中打开 /tmp/https_traffic.pcap
# 并配置密钥文件 /tmp/ssl_keys.log
```

## 🆚 对比：环境变量 vs 代码注入

| 特性 | 环境变量方法 | 代码注入方法 |
|------|-------------|-------------|
| **复杂度** | ⭐ 简单 | ⭐⭐⭐⭐⭐ 复杂 |
| **权限要求** | ⭐ 用户权限 | ⭐⭐⭐⭐⭐ Root权限 |
| **兼容性** | ⭐⭐⭐⭐ 广泛支持 | ⭐⭐⭐ 部分支持 |
| **稳定性** | ⭐⭐⭐⭐⭐ 非常稳定 | ⭐⭐⭐ 可能不稳定 |
| **安全性** | ⭐⭐⭐⭐⭐ 安全 | ⭐⭐ 有风险 |
| **维护成本** | ⭐ 低 | ⭐⭐⭐⭐⭐ 高 |

## 💡 最佳实践

1. **优先使用环境变量方法**
2. **只在环境变量方法不可用时才考虑代码注入**
3. **为不同的应用使用不同的密钥文件**
4. **定期清理旧的密钥文件**
5. **在生产环境中谨慎使用**

## 🔗 相关资源

- [OpenSSL 官方文档](https://www.openssl.org/docs/)
- [Wireshark TLS 解密指南](https://wiki.wireshark.org/TLS)
- [NSS Key Log Format](https://developer.mozilla.org/en-US/docs/Mozilla/Projects/NSS/Key_Log_Format)
