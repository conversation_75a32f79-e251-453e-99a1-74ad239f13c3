#!/bin/bash

# SSL Key Log 方法对比演示
# 对比环境变量方法 vs 代码注入方法

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  $1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

log_section() {
    echo -e "${CYAN}--- $1 ---${NC}"
}

# 清理函数
cleanup() {
    log_info "清理测试文件..."
    rm -f /tmp/env_test_*.log
    rm -f /tmp/injection_test_*.log
    unset SSLKEYLOGFILE
}

# 信号处理
trap cleanup EXIT

# 测试环境变量方法
test_environment_method() {
    log_header "测试环境变量方法"
    echo ""
    
    log_section "方法特点"
    echo "✅ 官方支持 - OpenSSL 原生支持"
    echo "✅ 无需特殊权限 - 普通用户即可使用"
    echo "✅ 配置简单 - 只需设置环境变量"
    echo "✅ 格式标准 - Wireshark 直接支持"
    echo "✅ 更安全 - 不需要代码注入"
    echo ""
    
    log_section "实际测试"
    
    # 设置环境变量
    local keylog_file="/tmp/env_test_$(date +%s).log"
    export SSLKEYLOGFILE="$keylog_file"
    
    log_info "设置环境变量: SSLKEYLOGFILE=$keylog_file"
    
    # 测试命令列表
    local test_commands=(
        "curl -s -k https://httpbin.org/get"
        "python3 -c \"import ssl, urllib.request; urllib.request.urlopen('https://httpbin.org/get', context=ssl._create_unverified_context())\""
    )
    
    local success_count=0
    local total_keys=0
    
    for cmd in "${test_commands[@]}"; do
        log_info "执行: $cmd"
        
        # 清空文件
        > "$keylog_file"
        
        # 执行命令
        if eval "$cmd" >/dev/null 2>&1; then
            if [[ -s "$keylog_file" ]]; then
                local key_count=$(grep -c "CLIENT_RANDOM" "$keylog_file" 2>/dev/null || echo "0")
                if [[ $key_count -gt 0 ]]; then
                    log_info "✅ 成功提取 $key_count 个密钥"
                    ((success_count++))
                    ((total_keys += key_count))
                else
                    log_warn "⚠️  命令执行成功但未提取到密钥"
                fi
            else
                log_warn "⚠️  命令执行成功但未生成 keylog 文件"
            fi
        else
            log_warn "⚠️  命令执行失败"
        fi
        echo ""
    done
    
    log_section "环境变量方法结果"
    echo "成功命令: $success_count/${#test_commands[@]}"
    echo "提取密钥: $total_keys 个"
    echo "配置复杂度: ⭐ (非常简单)"
    echo "权限要求: ⭐ (普通用户)"
    echo "稳定性: ⭐⭐⭐⭐⭐ (非常稳定)"
    echo ""
    
    # 显示密钥文件内容示例
    if [[ -s "$keylog_file" ]]; then
        log_section "密钥文件内容示例"
        head -3 "$keylog_file"
        echo ""
    fi
}

# 分析代码注入方法
analyze_injection_method() {
    log_header "分析代码注入方法"
    echo ""
    
    log_section "方法特点"
    echo "❌ 复杂实现 - 需要编写 C 代码"
    echo "❌ 需要 Root 权限 - 系统级操作"
    echo "❌ 配置复杂 - 多个配置文件"
    echo "❌ 兼容性问题 - 可能与某些程序冲突"
    echo "❌ 安全风险 - 代码注入有潜在风险"
    echo ""
    
    log_section "代码注入方法分析"
    
    # 检查注入器代码
    local injector_file="ssl_key_extractor/src/keylog_injector.c"
    if [[ -f "$injector_file" ]]; then
        local line_count=$(wc -l < "$injector_file")
        log_info "注入器代码行数: $line_count 行"
        
        # 分析代码复杂度
        local function_count=$(grep -c "^[a-zA-Z_][a-zA-Z0-9_]* .*(" "$injector_file" || echo "0")
        log_info "函数数量: $function_count 个"
        
        # 检查依赖
        local includes=$(grep -c "#include" "$injector_file" || echo "0")
        log_info "头文件依赖: $includes 个"
        
        echo ""
        log_info "代码复杂度分析:"
        echo "  - 需要理解 OpenSSL 内部机制"
        echo "  - 需要处理动态库加载"
        echo "  - 需要处理多进程/多线程安全"
        echo "  - 需要处理错误恢复"
        echo "  - 需要维护兼容性"
    else
        log_warn "未找到注入器代码文件"
    fi
    
    echo ""
    log_section "配置文件分析")
    
    # 检查配置脚本
    local setup_script="ssl_key_extractor/setup_global_injection.sh"
    if [[ -f "$setup_script" ]]; then
        local script_lines=$(wc -l < "$setup_script")
        log_info "配置脚本行数: $script_lines 行"
        
        echo ""
        log_info "配置复杂度:"
        echo "  - 需要编译 C 代码"
        echo "  - 需要配置 ld.so.preload"
        echo "  - 需要创建系统配置文件"
        echo "  - 需要处理权限问题"
        echo "  - 需要重启服务"
    else
        log_warn "未找到配置脚本"
    fi
    
    echo ""
    log_section "代码注入方法评估"
    echo "配置复杂度: ⭐⭐⭐⭐⭐ (非常复杂)"
    echo "权限要求: ⭐⭐⭐⭐⭐ (需要 Root)"
    echo "稳定性: ⭐⭐⭐ (可能不稳定)"
    echo "维护成本: ⭐⭐⭐⭐⭐ (非常高)"
    echo "安全风险: ⭐⭐⭐ (有潜在风险)"
    echo ""
}

# 显示对比总结
show_comparison_summary() {
    log_header "方法对比总结"
    echo ""
    
    printf "%-20s %-20s %-20s\n" "特性" "环境变量方法" "代码注入方法"
    printf "%-20s %-20s %-20s\n" "----" "----------" "----------"
    printf "%-20s %-20s %-20s\n" "复杂度" "⭐ 简单" "⭐⭐⭐⭐⭐ 复杂"
    printf "%-20s %-20s %-20s\n" "权限要求" "⭐ 用户权限" "⭐⭐⭐⭐⭐ Root权限"
    printf "%-20s %-20s %-20s\n" "兼容性" "⭐⭐⭐⭐ 广泛支持" "⭐⭐⭐ 部分支持"
    printf "%-20s %-20s %-20s\n" "稳定性" "⭐⭐⭐⭐⭐ 非常稳定" "⭐⭐⭐ 可能不稳定"
    printf "%-20s %-20s %-20s\n" "安全性" "⭐⭐⭐⭐⭐ 安全" "⭐⭐ 有风险"
    printf "%-20s %-20s %-20s\n" "维护成本" "⭐ 低" "⭐⭐⭐⭐⭐ 高"
    
    echo ""
    log_section "推荐方案"
    echo "🎯 强烈推荐使用环境变量方法 (SSLKEYLOGFILE)"
    echo ""
    echo "理由:"
    echo "1. ✅ 官方支持，稳定可靠"
    echo "2. ✅ 配置简单，一行命令搞定"
    echo "3. ✅ 无需特殊权限"
    echo "4. ✅ 广泛兼容各种程序"
    echo "5. ✅ 安全无风险"
    echo ""
    echo "使用方法:"
    echo "  export SSLKEYLOGFILE=\"/tmp/ssl_keys.log\""
    echo "  curl -k https://httpbin.org/get"
    echo ""
    echo "或使用配置脚本:"
    echo "  ./ssl_key_extractor/setup_env_keylog.sh enable"
    echo ""
}

# 显示实用建议
show_practical_advice() {
    log_header "实用建议"
    echo ""
    
    log_section "何时使用环境变量方法"
    echo "✅ 开发和测试环境"
    echo "✅ 需要分析 curl、wget、Python 等程序的 HTTPS 流量"
    echo "✅ 临时调试 SSL/TLS 问题"
    echo "✅ 与 Wireshark 配合进行流量分析"
    echo ""
    
    log_section "何时考虑代码注入方法"
    echo "⚠️  程序不支持 SSLKEYLOGFILE 环境变量"
    echo "⚠️  需要对系统级服务进行分析"
    echo "⚠️  程序使用了特殊的 SSL 库"
    echo ""
    echo "注意: 即使在这些情况下，也应该先尝试其他方法:"
    echo "  - 检查程序是否有内置的密钥导出功能"
    echo "  - 尝试修改程序配置"
    echo "  - 考虑使用其他分析工具"
    echo ""
    
    log_section "最佳实践"
    echo "1. 🎯 优先使用环境变量方法"
    echo "2. 🔒 为不同应用使用不同的密钥文件"
    echo "3. 🧹 定期清理旧的密钥文件"
    echo "4. ⚠️  在生产环境中谨慎使用"
    echo "5. 📚 了解目标程序的 SSL 库依赖"
    echo ""
}

# 主函数
main() {
    echo ""
    log_header "SSL Key Log 方法对比演示"
    echo ""
    
    # 检查网络连接
    if ! ping -c 1 httpbin.org >/dev/null 2>&1; then
        log_warn "网络连接不可用，将跳过实际测试"
        SKIP_NETWORK_TEST=true
    fi
    
    # 测试环境变量方法
    if [[ "$SKIP_NETWORK_TEST" != "true" ]]; then
        test_environment_method
    fi
    
    # 分析代码注入方法
    analyze_injection_method
    
    # 显示对比总结
    show_comparison_summary
    
    # 显示实用建议
    show_practical_advice
    
    log_header "演示完成"
    echo ""
    log_info "如需启用环境变量方法，请运行:"
    echo "  ./ssl_key_extractor/setup_env_keylog.sh enable"
    echo ""
}

# 运行主函数
main "$@"
