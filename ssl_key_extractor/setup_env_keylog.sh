#!/bin/bash

# SSL Key Log 环境变量配置脚本
# 使用 SSLKEYLOGFILE 环境变量直接生成 SSL key log
# 无需复杂的代码注入，更简单、更可靠

set -e

# 配置
DEFAULT_KEYLOG_FILE="/tmp/ssl_keylog.txt"
SYSTEMD_ENV_DIR="/etc/systemd/system"
PROFILE_FILE="/etc/profile.d/ssl-keylog.sh"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  SSL Key Log 环境变量配置工具${NC}"
    echo -e "${BLUE}  基于 SSLKEYLOGFILE 环境变量${NC}"
    echo -e "${BLUE}========================================${NC}"
}

# 显示帮助信息
show_help() {
    cat << EOF
用法: $0 [选项] [命令]

命令:
  enable [文件路径]     启用 SSL key logging (默认: $DEFAULT_KEYLOG_FILE)
  disable              禁用 SSL key logging
  status               显示当前状态
  test                 测试 SSL key logging
  help                 显示此帮助信息

选项:
  --global             全局启用（所有用户和服务）
  --user               仅当前用户启用
  --service <名称>     为特定服务启用

示例:
  $0 enable                                    # 启用默认配置
  $0 enable /var/log/ssl_keys.log             # 指定文件路径
  $0 enable --global                          # 全局启用
  $0 enable --service nginx                   # 为nginx服务启用
  $0 disable                                  # 禁用
  $0 test                                     # 测试功能

EOF
}

# 检查权限
check_permissions() {
    if [[ "$GLOBAL_MODE" == "true" ]] && [[ $EUID -ne 0 ]]; then
        log_error "全局模式需要 root 权限"
        log_info "请使用: sudo $0 $*"
        exit 1
    fi
}

# 创建 SSL key log 文件
create_keylog_file() {
    local keylog_file="$1"
    
    # 创建目录
    local dir=$(dirname "$keylog_file")
    mkdir -p "$dir"
    
    # 创建文件
    touch "$keylog_file"
    chmod 600 "$keylog_file"
    
    log_info "✅ SSL key log 文件已创建: $keylog_file"
}

# 全局启用（所有用户和服务）
enable_global() {
    local keylog_file="$1"
    
    log_info "启用全局 SSL key logging..."
    
    # 创建系统级环境变量文件
    cat > "$PROFILE_FILE" << EOF
#!/bin/bash
# SSL Key Log 环境变量配置
# 自动生成于: $(date)

# 设置 SSLKEYLOGFILE 环境变量
export SSLKEYLOGFILE="$keylog_file"

# 确保文件存在且权限正确
if [[ ! -f "\$SSLKEYLOGFILE" ]]; then
    mkdir -p "\$(dirname "\$SSLKEYLOGFILE")"
    touch "\$SSLKEYLOGFILE"
    chmod 600 "\$SSLKEYLOGFILE"
fi
EOF
    
    chmod 644 "$PROFILE_FILE"
    
    # 创建 keylog 文件
    create_keylog_file "$keylog_file"
    
    log_info "✅ 全局 SSL key logging 已启用"
    log_info "   环境变量文件: $PROFILE_FILE"
    log_info "   SSL key log 文件: $keylog_file"
    log_warn "   需要重新登录或重启服务以生效"
}

# 用户级启用
enable_user() {
    local keylog_file="$1"
    local shell_rc=""
    
    log_info "启用用户级 SSL key logging..."
    
    # 检测用户的 shell
    if [[ -f "$HOME/.bashrc" ]]; then
        shell_rc="$HOME/.bashrc"
    elif [[ -f "$HOME/.zshrc" ]]; then
        shell_rc="$HOME/.zshrc"
    else
        shell_rc="$HOME/.profile"
    fi
    
    # 检查是否已经配置
    if grep -q "SSLKEYLOGFILE" "$shell_rc" 2>/dev/null; then
        log_warn "SSL key logging 配置已存在于 $shell_rc"
        log_info "正在更新配置..."
        sed -i '/SSLKEYLOGFILE/d' "$shell_rc"
    fi
    
    # 添加配置
    cat >> "$shell_rc" << EOF

# SSL Key Log 配置 (自动添加于 $(date))
export SSLKEYLOGFILE="$keylog_file"
EOF
    
    # 创建 keylog 文件
    create_keylog_file "$keylog_file"
    
    # 设置当前会话的环境变量
    export SSLKEYLOGFILE="$keylog_file"
    
    log_info "✅ 用户级 SSL key logging 已启用"
    log_info "   配置文件: $shell_rc"
    log_info "   SSL key log 文件: $keylog_file"
    log_info "   当前会话已生效，新会话需要重新登录"
}

# 为特定服务启用
enable_service() {
    local service_name="$1"
    local keylog_file="$2"
    
    log_info "为服务 '$service_name' 启用 SSL key logging..."
    
    # 检查服务是否存在
    if ! systemctl list-unit-files | grep -q "^$service_name.service"; then
        log_error "服务 '$service_name' 不存在"
        return 1
    fi
    
    # 创建服务环境变量目录
    local service_env_dir="$SYSTEMD_ENV_DIR/$service_name.service.d"
    mkdir -p "$service_env_dir"
    
    # 创建环境变量配置文件
    cat > "$service_env_dir/ssl-keylog.conf" << EOF
[Service]
Environment="SSLKEYLOGFILE=$keylog_file"
EOF
    
    # 创建 keylog 文件
    create_keylog_file "$keylog_file"
    
    # 重新加载 systemd 配置
    systemctl daemon-reload
    
    log_info "✅ 服务 '$service_name' SSL key logging 已启用"
    log_info "   配置文件: $service_env_dir/ssl-keylog.conf"
    log_info "   SSL key log 文件: $keylog_file"
    log_warn "   需要重启服务以生效: systemctl restart $service_name"
}

# 禁用 SSL key logging
disable_ssl_keylog() {
    log_info "禁用 SSL key logging..."
    
    local disabled_count=0
    
    # 删除全局配置
    if [[ -f "$PROFILE_FILE" ]]; then
        rm -f "$PROFILE_FILE"
        log_info "✅ 已删除全局配置: $PROFILE_FILE"
        ((disabled_count++))
    fi
    
    # 删除用户配置
    for rc_file in "$HOME/.bashrc" "$HOME/.zshrc" "$HOME/.profile"; do
        if [[ -f "$rc_file" ]] && grep -q "SSLKEYLOGFILE" "$rc_file"; then
            sed -i '/SSL Key Log 配置/,+1d' "$rc_file"
            sed -i '/SSLKEYLOGFILE/d' "$rc_file"
            log_info "✅ 已从 $rc_file 删除配置"
            ((disabled_count++))
        fi
    done
    
    # 删除服务配置
    if [[ -d "$SYSTEMD_ENV_DIR" ]]; then
        find "$SYSTEMD_ENV_DIR" -name "ssl-keylog.conf" -delete 2>/dev/null || true
        systemctl daemon-reload 2>/dev/null || true
        log_info "✅ 已删除所有服务配置"
        ((disabled_count++))
    fi
    
    # 清除当前会话环境变量
    unset SSLKEYLOGFILE
    
    if [[ $disabled_count -gt 0 ]]; then
        log_info "✅ SSL key logging 已禁用"
        log_warn "   需要重新登录或重启服务以完全生效"
    else
        log_info "ℹ️  未发现 SSL key logging 配置"
    fi
}

# 显示状态
show_status() {
    log_header
    echo ""
    
    log_info "SSL Key Logging 状态检查:"
    echo ""
    
    # 检查当前环境变量
    if [[ -n "$SSLKEYLOGFILE" ]]; then
        echo -e "  ${GREEN}✅ 当前会话已启用${NC}"
        echo -e "     文件: $SSLKEYLOGFILE"
        if [[ -f "$SSLKEYLOGFILE" ]]; then
            local size=$(stat -f%z "$SSLKEYLOGFILE" 2>/dev/null || stat -c%s "$SSLKEYLOGFILE" 2>/dev/null || echo "0")
            echo -e "     大小: $size 字节"
        else
            echo -e "     ${YELLOW}⚠️  文件不存在${NC}"
        fi
    else
        echo -e "  ${RED}❌ 当前会话未启用${NC}"
    fi
    
    echo ""
    
    # 检查全局配置
    if [[ -f "$PROFILE_FILE" ]]; then
        echo -e "  ${GREEN}✅ 全局配置已启用${NC}"
        echo -e "     配置文件: $PROFILE_FILE"
    else
        echo -e "  ${RED}❌ 全局配置未启用${NC}"
    fi
    
    echo ""
    
    # 检查用户配置
    local user_configured=false
    for rc_file in "$HOME/.bashrc" "$HOME/.zshrc" "$HOME/.profile"; do
        if [[ -f "$rc_file" ]] && grep -q "SSLKEYLOGFILE" "$rc_file"; then
            echo -e "  ${GREEN}✅ 用户配置已启用${NC}"
            echo -e "     配置文件: $rc_file"
            user_configured=true
            break
        fi
    done
    
    if [[ "$user_configured" == "false" ]]; then
        echo -e "  ${RED}❌ 用户配置未启用${NC}"
    fi
    
    echo ""
    
    # 检查服务配置
    if [[ -d "$SYSTEMD_ENV_DIR" ]]; then
        local service_configs=$(find "$SYSTEMD_ENV_DIR" -name "ssl-keylog.conf" 2>/dev/null | wc -l)
        if [[ $service_configs -gt 0 ]]; then
            echo -e "  ${GREEN}✅ 服务配置已启用${NC} ($service_configs 个服务)"
            find "$SYSTEMD_ENV_DIR" -name "ssl-keylog.conf" 2>/dev/null | while read config; do
                local service=$(basename $(dirname "$config") .service.d)
                echo -e "     服务: $service"
            done
        else
            echo -e "  ${RED}❌ 服务配置未启用${NC}"
        fi
    else
        echo -e "  ${RED}❌ 服务配置未启用${NC}"
    fi
    
    echo ""
}

# 测试 SSL key logging
test_ssl_keylog() {
    log_info "测试 SSL key logging 功能..."

    # 检查环境变量
    if [[ -z "$SSLKEYLOGFILE" ]]; then
        log_error "SSLKEYLOGFILE 环境变量未设置"
        log_info "请先启用 SSL key logging: $0 enable"
        return 1
    fi

    log_info "当前配置: SSLKEYLOGFILE=$SSLKEYLOGFILE"

    # 创建测试文件
    local test_keylog="/tmp/ssl_keylog_test_$(date +%s).txt"
    export SSLKEYLOGFILE="$test_keylog"

    log_info "执行测试 HTTPS 请求..."

    # 测试命令列表
    local test_commands=(
        "curl -s -k https://httpbin.org/get"
        "wget -q --no-check-certificate -O /dev/null https://www.google.com"
        "python3 -c \"import ssl, urllib.request; urllib.request.urlopen('https://httpbin.org/get', context=ssl._create_unverified_context())\""
    )

    local success_count=0

    for cmd in "${test_commands[@]}"; do
        log_info "测试命令: $cmd"

        # 清空测试文件
        > "$test_keylog"

        # 执行命令
        if eval "$cmd" >/dev/null 2>&1; then
            # 检查是否生成了密钥
            if [[ -s "$test_keylog" ]]; then
                local key_count=$(grep -c "CLIENT_RANDOM" "$test_keylog" 2>/dev/null || echo "0")
                if [[ $key_count -gt 0 ]]; then
                    log_info "✅ 成功提取 $key_count 个密钥"
                    ((success_count++))
                else
                    log_warn "⚠️  命令执行成功但未提取到密钥"
                fi
            else
                log_warn "⚠️  命令执行成功但未生成 keylog 文件"
            fi
        else
            log_warn "⚠️  命令执行失败"
        fi

        echo ""
    done

    # 清理测试文件
    rm -f "$test_keylog"

    if [[ $success_count -gt 0 ]]; then
        log_info "✅ SSL key logging 测试通过 ($success_count/${#test_commands[@]} 个命令成功)"
    else
        log_error "❌ SSL key logging 测试失败"
        log_info "可能的原因:"
        log_info "  1. 程序不支持 SSLKEYLOGFILE 环境变量"
        log_info "  2. OpenSSL 版本过旧"
        log_info "  3. 程序使用了其他 SSL 库"
    fi
}

# 主函数
main() {
    # 解析参数
    GLOBAL_MODE=false
    USER_MODE=false
    SERVICE_MODE=false
    SERVICE_NAME=""

    while [[ $# -gt 0 ]]; do
        case $1 in
            --global)
                GLOBAL_MODE=true
                shift
                ;;
            --user)
                USER_MODE=true
                shift
                ;;
            --service)
                SERVICE_MODE=true
                SERVICE_NAME="$2"
                shift 2
                ;;
            enable)
                COMMAND="enable"
                KEYLOG_FILE="${2:-$DEFAULT_KEYLOG_FILE}"
                shift
                if [[ "$1" != --* ]] && [[ -n "$1" ]]; then
                    shift
                fi
                ;;
            disable)
                COMMAND="disable"
                shift
                ;;
            status)
                COMMAND="status"
                shift
                ;;
            test)
                COMMAND="test"
                shift
                ;;
            help|--help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 默认命令
    if [[ -z "$COMMAND" ]]; then
        COMMAND="status"
    fi

    # 检查权限
    check_permissions

    # 执行命令
    case "$COMMAND" in
        enable)
            log_header
            echo ""

            if [[ "$SERVICE_MODE" == "true" ]]; then
                if [[ -z "$SERVICE_NAME" ]]; then
                    log_error "请指定服务名称"
                    exit 1
                fi
                enable_service "$SERVICE_NAME" "$KEYLOG_FILE"
            elif [[ "$GLOBAL_MODE" == "true" ]]; then
                enable_global "$KEYLOG_FILE"
            else
                enable_user "$KEYLOG_FILE"
            fi
            ;;
        disable)
            disable_ssl_keylog
            ;;
        status)
            show_status
            ;;
        test)
            test_ssl_keylog
            ;;
        *)
            log_error "未知命令: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
