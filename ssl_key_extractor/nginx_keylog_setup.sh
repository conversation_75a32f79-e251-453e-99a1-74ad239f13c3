#!/bin/bash

# Nginx SSL Key Logging 配置脚本
# 使用代码注入方法为 Nginx 启用 SSL key logging

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  $1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要 root 权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查 nginx
    if ! command -v nginx >/dev/null 2>&1; then
        log_error "未找到 nginx，请先安装"
        exit 1
    fi
    
    # 检查编译工具
    if ! command -v gcc >/dev/null 2>&1; then
        log_error "未找到 gcc，请先安装: yum install gcc 或 apt install gcc"
        exit 1
    fi
    
    # 检查 OpenSSL 开发库
    if ! find /usr/include -name "openssl" -type d 2>/dev/null | grep -q openssl; then
        log_warn "可能缺少 OpenSSL 开发库"
        log_info "请安装: yum install openssl-devel 或 apt install libssl-dev"
    fi
    
    log_info "✅ 依赖检查完成"
}

# 编译注入器
compile_injector() {
    log_info "编译 SSL key log 注入器..."
    
    local src_dir="$(pwd)/ssl_key_extractor/src"
    local injector_src="$src_dir/keylog_injector.c"
    local injector_lib="$(pwd)/ssl_key_extractor/libkeylog_injector.so"
    
    if [[ ! -f "$injector_src" ]]; then
        log_error "未找到注入器源码: $injector_src"
        exit 1
    fi
    
    # 编译
    gcc -shared -fPIC -o "$injector_lib" "$injector_src" -ldl -lssl -lcrypto
    
    if [[ $? -eq 0 ]] && [[ -f "$injector_lib" ]]; then
        log_info "✅ 注入器编译成功: $injector_lib"
    else
        log_error "❌ 注入器编译失败"
        exit 1
    fi
}

# 配置全局注入
setup_global_injection() {
    log_info "配置全局 SSL key log 注入..."
    
    local injector_lib="$(pwd)/ssl_key_extractor/libkeylog_injector.so"
    local keylog_file="/var/log/nginx/ssl_keylog.txt"
    local config_file="/etc/ssl-keylog.conf"
    local preload_file="/etc/ld.so.preload"
    
    # 创建配置文件
    cat > "$config_file" << EOF
# SSL KEYLOG 全局配置文件
# 此文件被 libkeylog_injector.so 读取

# SSL密钥输出文件路径
KEYLOG_FILE=$keylog_file

# 静默模式（1=启用，0=禁用）
KEYLOG_SILENT=1

# 详细日志（仅调试时启用）
KEYLOG_ENABLE_DETAILED_LOG=0
EOF
    
    chmod 644 "$config_file"
    log_info "✅ 配置文件已创建: $config_file"
    
    # 创建密钥文件目录
    mkdir -p "$(dirname "$keylog_file")"
    touch "$keylog_file"
    chmod 600 "$keylog_file"
    log_info "✅ 密钥文件已创建: $keylog_file"
    
    # 配置预加载
    if ! grep -q "$injector_lib" "$preload_file" 2>/dev/null; then
        echo "$injector_lib" >> "$preload_file"
        log_info "✅ 已添加到预加载列表: $preload_file"
    else
        log_info "ℹ️  预加载配置已存在"
    fi
}

# 重启 nginx
restart_nginx() {
    log_info "重启 Nginx 服务..."
    
    # 检查 nginx 配置
    if nginx -t; then
        log_info "✅ Nginx 配置文件语法正确"
    else
        log_error "❌ Nginx 配置文件有错误，请检查"
        return 1
    fi
    
    # 重启服务
    if systemctl restart nginx; then
        log_info "✅ Nginx 重启成功"
        sleep 2
        
        # 检查服务状态
        if systemctl is-active nginx >/dev/null; then
            log_info "✅ Nginx 服务运行正常"
        else
            log_error "❌ Nginx 服务启动失败"
            return 1
        fi
    else
        log_error "❌ Nginx 重启失败"
        return 1
    fi
}

# 测试配置
test_configuration() {
    log_info "测试 SSL key logging 配置..."
    
    local keylog_file="/var/log/nginx/ssl_keylog.txt"
    local test_url="https://localhost"
    
    # 清空密钥文件
    > "$keylog_file"
    
    # 检查 nginx 是否监听 HTTPS 端口
    local https_port=$(netstat -tlnp | grep nginx | grep :443 | head -1 | awk '{print $4}' | cut -d: -f2)
    if [[ -n "$https_port" ]]; then
        test_url="https://localhost:$https_port"
    else
        log_warn "未检测到 nginx HTTPS 端口，使用默认 443"
    fi
    
    log_info "执行测试请求: $test_url"
    
    # 执行测试请求
    if curl -k -s "$test_url" >/dev/null 2>&1; then
        log_info "✅ 测试请求执行成功"
        
        # 等待密钥写入
        sleep 1
        
        # 检查密钥文件
        if [[ -s "$keylog_file" ]]; then
            local key_count=$(grep -c "CLIENT_RANDOM" "$keylog_file" 2>/dev/null || echo "0")
            if [[ $key_count -gt 0 ]]; then
                log_info "✅ 成功提取 $key_count 个 SSL 密钥"
                log_info "密钥文件: $keylog_file"
                return 0
            else
                log_warn "⚠️  密钥文件存在但未包含有效密钥"
            fi
        else
            log_warn "⚠️  未生成密钥文件或文件为空"
        fi
    else
        log_warn "⚠️  测试请求失败，可能是 nginx 配置问题"
    fi
    
    return 1
}

# 显示状态
show_status() {
    log_header "Nginx SSL Key Logging 状态"
    echo ""
    
    local injector_lib="$(pwd)/ssl_key_extractor/libkeylog_injector.so"
    local keylog_file="/var/log/nginx/ssl_keylog.txt"
    local config_file="/etc/ssl-keylog.conf"
    local preload_file="/etc/ld.so.preload"
    
    # 检查注入器库
    if [[ -f "$injector_lib" ]]; then
        echo -e "  ${GREEN}✅ 注入器库存在${NC}: $injector_lib"
    else
        echo -e "  ${RED}❌ 注入器库不存在${NC}: $injector_lib"
    fi
    
    # 检查配置文件
    if [[ -f "$config_file" ]]; then
        echo -e "  ${GREEN}✅ 配置文件存在${NC}: $config_file"
    else
        echo -e "  ${RED}❌ 配置文件不存在${NC}: $config_file"
    fi
    
    # 检查预加载配置
    if grep -q "$injector_lib" "$preload_file" 2>/dev/null; then
        echo -e "  ${GREEN}✅ 预加载配置已启用${NC}"
    else
        echo -e "  ${RED}❌ 预加载配置未启用${NC}"
    fi
    
    # 检查密钥文件
    if [[ -f "$keylog_file" ]]; then
        local size=$(stat -c%s "$keylog_file" 2>/dev/null || echo "0")
        echo -e "  ${GREEN}✅ 密钥文件存在${NC}: $keylog_file (大小: $size 字节)"
    else
        echo -e "  ${RED}❌ 密钥文件不存在${NC}: $keylog_file"
    fi
    
    # 检查 nginx 状态
    if systemctl is-active nginx >/dev/null; then
        echo -e "  ${GREEN}✅ Nginx 服务运行中${NC}"
        
        # 检查进程是否加载了注入器
        local nginx_pids=$(pgrep nginx)
        local loaded_count=0
        for pid in $nginx_pids; do
            if pmap "$pid" 2>/dev/null | grep -q keylog; then
                ((loaded_count++))
            fi
        done
        
        if [[ $loaded_count -gt 0 ]]; then
            echo -e "  ${GREEN}✅ 注入器已加载${NC} ($loaded_count 个进程)"
        else
            echo -e "  ${YELLOW}⚠️  注入器未加载到 nginx 进程${NC}"
        fi
    else
        echo -e "  ${RED}❌ Nginx 服务未运行${NC}"
    fi
    
    echo ""
}

# 清理配置
cleanup_configuration() {
    log_info "清理 SSL key logging 配置..."
    
    local injector_lib="$(pwd)/ssl_key_extractor/libkeylog_injector.so"
    local config_file="/etc/ssl-keylog.conf"
    local preload_file="/etc/ld.so.preload"
    
    # 从预加载列表中移除
    if [[ -f "$preload_file" ]]; then
        sed -i "\|$injector_lib|d" "$preload_file"
        log_info "✅ 已从预加载列表移除"
    fi
    
    # 删除配置文件
    if [[ -f "$config_file" ]]; then
        rm -f "$config_file"
        log_info "✅ 已删除配置文件"
    fi
    
    log_info "✅ 清理完成，请重启 nginx: systemctl restart nginx"
}

# 显示帮助
show_help() {
    cat << EOF
用法: $0 [命令]

命令:
  setup     设置 Nginx SSL key logging（编译+配置+重启）
  compile   仅编译注入器
  enable    仅启用配置（需要先编译）
  test      测试配置
  status    显示状态
  cleanup   清理配置
  help      显示此帮助

示例:
  $0 setup      # 完整设置
  $0 status     # 查看状态
  $0 test       # 测试功能
  $0 cleanup    # 清理配置

EOF
}

# 主函数
main() {
    local command="${1:-help}"
    
    case "$command" in
        setup)
            log_header "设置 Nginx SSL Key Logging"
            check_permissions
            check_dependencies
            compile_injector
            setup_global_injection
            restart_nginx
            echo ""
            if test_configuration; then
                log_info "🎉 Nginx SSL key logging 设置成功！"
            else
                log_warn "⚠️  设置完成但测试未通过，请检查配置"
            fi
            show_status
            ;;
        compile)
            check_permissions
            check_dependencies
            compile_injector
            ;;
        enable)
            check_permissions
            setup_global_injection
            restart_nginx
            ;;
        test)
            test_configuration
            ;;
        status)
            show_status
            ;;
        cleanup)
            check_permissions
            cleanup_configuration
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
